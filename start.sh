#!/bin/bash

echo "Starting Terminal Interface (Full ANSI Color Support)..."
echo "Make sure your terminal window is at least 120x30 characters"
echo "This version supports full ANSI color output from subprocesses!"
echo ""

# Check if Python 3 is available
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    # Check if python is Python 3
    PYTHON_VERSION=$(python -c "import sys; print(sys.version_info.major)")
    if [ "$PYTHON_VERSION" = "3" ]; then
        PYTHON_CMD="python"
    else
        echo "Error: Python 3 is required but not found."
        echo "Please install Python 3 or ensure 'python3' command is available."
        read -p "Press Enter to exit..."
        exit 1
    fi
else
    echo "Error: Python is not installed or not in PATH."
    echo "Please install Python 3 and try again."
    read -p "Press Enter to exit..."
    exit 1
fi

# Set environment variables for better color support
export FORCE_COLOR=1
export COLORTERM=truecolor
export TERM=xterm-256color

# Run the Python script
$PYTHON_CMD ./scripts/start.py

# Wait for user input before closing (equivalent to pause in batch)
read -p "Press Enter to continue..."
